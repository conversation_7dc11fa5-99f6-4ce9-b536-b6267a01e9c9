<template>
  <div class="template-card">
    <div class="template-header">
      <div class="template-name">{{ template.name || '未命名模板' }}</div>
      <div class="template-path">{{ template.path }}</div>
    </div>
    
    <div class="template-preview-container" @click="openModal" ref="previewContainer">
      <!-- 懒加载占位符 -->
      <div v-if="!isVisible" class="template-placeholder">
        <div class="placeholder-content">
          <div class="placeholder-icon">📄</div>
          <div class="placeholder-text">模板预览</div>
          <div class="placeholder-subtext">滚动到此处加载</div>
        </div>
      </div>
      
      <!-- 加载中状态 -->
      <div v-if="isVisible && !isLoaded" class="template-loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载...</div>
      </div>
      
      <!-- 实际的iframe -->
      <iframe 
        v-if="isVisible"
        :src="getPreviewUrl(template.path)" 
        class="template-preview-iframe"
        frameborder="0"
        scrolling="no"
        @load="handleIframeLoad"
        ref="previewIframe"
      ></iframe>
      
      <div class="preview-overlay">
        <div class="preview-overlay-content">
          <el-icon class="expand-icon"><ZoomIn /></el-icon>
          <span>点击查看详情</span>
        </div>
      </div>
    </div>

    <!-- Element Plus Dialog -->
    <el-dialog
      v-model="isModalOpen"
      :title="template.name || '未命名模板'"
      width="70%"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      :append-to-body="true"
      class="template-dialog"
    >
      <div class="dialog-content">
        <div class="dialog-left">
          <div class="template-info">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="模板名称">
                {{ template.name || '未命名模板' }}
              </el-descriptions-item>
              <el-descriptions-item label="文件路径">
                {{ template.path }}
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatDate(new Date()) }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          
          <div class="dialog-actions">
            <el-button 
              type="primary" 
              @click="openInNewWindow"
            >
              在新窗口打开
            </el-button>
          </div>
          <div class="dialog-actions">
            <el-button 
              @click="copyPath"
            >
              复制路径
            </el-button>
          </div>
        </div>
        
        <div class="dialog-right">
          <div class="dialog-preview-container">
            <iframe 
              :src="getPreviewUrl(template.path)" 
              class="dialog-preview-iframe"
              frameborder="0"
            ></iframe>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { ZoomIn } from '@element-plus/icons-vue'

export default {
  name: 'TemplateCard',
  components: {
    ZoomIn
  },
  props: {
    template: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const isModalOpen = ref(false)
    const previewIframe = ref(null)
    const previewContainer = ref(null)
    const isVisible = ref(false)
    const isLoaded = ref(false)
    let observer = null

    const openModal = () => {
      isModalOpen.value = true
    }

    const getPreviewUrl = (path) => {
      return path
    }

    const handleIframeLoad = () => {
      console.log('Preview iframe loaded')
      isLoaded.value = true
    }

    const formatDate = (date) => {
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const openInNewWindow = () => {
      window.open(getPreviewUrl(props.template.path), '_blank')
    }

    const copyPath = async () => {
      try {
        await navigator.clipboard.writeText(getPreviewUrl(props.template.path))
        ElMessage.success('路径已复制到剪贴板')
      } catch (err) {
        console.error('复制失败:', err)
        ElMessage.error('复制失败，请手动复制')
      }
    }

    // 设置 Intersection Observer 进行懒加载
    const setupIntersectionObserver = () => {
      if (!window.IntersectionObserver) {
        // 如果浏览器不支持 Intersection Observer，直接显示
        isVisible.value = true
        return
      }

      observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            isVisible.value = true
            // 一旦加载就取消观察，避免重复加载
            observer.unobserve(entry.target)
          }
        })
      }, {
        // 在虚拟滚动环境下，减少提前加载距离以提高性能
        rootMargin: '50px',
        threshold: 0.1
      })

      if (previewContainer.value) {
        observer.observe(previewContainer.value)
      }
    }

    onMounted(() => {
      setupIntersectionObserver()
    })

    onUnmounted(() => {
      if (observer) {
        observer.disconnect()
      }
    })

    // 在虚拟滚动环境下，监听模板变化以重置状态
    watch(() => props.template.path, () => {
      // 当模板路径变化时（虚拟滚动复用组件），重置状态
      isVisible.value = false
      isLoaded.value = false
      if (observer) {
        observer.disconnect()
      }
      setupIntersectionObserver()
    })

    return {
      isModalOpen,
      previewIframe,
      previewContainer,
      isVisible,
      isLoaded,
      openModal,
      getPreviewUrl,
      handleIframeLoad,
      formatDate,
      openInNewWindow,
      copyPath
    }
  }
}
</script>

<style scoped>
.template-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: 380px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 移除hover动画效果 */
.template-card:hover {
  border-color: #3b82f6;
}

.template-header {
  margin-bottom: 18px;
}

.template-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 10px;
  font-size: 16px;
  line-height: 1.4;
}

.template-path {
  color: #6b7280;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', monospace;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  word-break: break-all;
}

.template-preview-container {
  position: relative;
  width: 100%;
  flex: 1; /* 占用剩余空间 */
  min-height: 150px; /* 最小高度 */
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 移除预览容器hover动画 */
.template-preview-container:hover {
  border-color: #3b82f6;
}

.template-preview-iframe {
  width: 333.33%;
  height: 333.33%;
  pointer-events: none;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.3);
}

/* 懒加载占位符样式 */
.template-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.placeholder-content {
  text-align: center;
  color: #64748b;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 8px;
  opacity: 0.6;
}

.placeholder-text {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.placeholder-subtext {
  font-size: 12px;
  opacity: 0.8;
}

/* 加载中状态样式 */
.template-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f8fafc;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #64748b;
  font-size: 14px;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.template-preview-container:hover .preview-overlay {
  background: rgba(0, 0, 0, 0.7);
}

.preview-overlay-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: white;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
}

.template-preview-container:hover .preview-overlay-content {
  opacity: 1;
  transform: translateY(0);
}

.expand-icon {
  width: 32px;
  height: 32px;
}

.preview-overlay-content span {
  font-size: 14px;
  font-weight: 500;
}

/* Dialog 样式 */
.dialog-content {
  display: flex;
  gap: 24px;
  height: 600px;
}

.dialog-left {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.template-info {
  flex: 1;
}

.dialog-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.dialog-right {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.dialog-preview-container {
  flex: 1;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.dialog-preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  min-height: 500px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dialog-content {
    flex-direction: column;
    height: auto;
  }
  
  .dialog-left {
    width: 100%;
  }
  
  .dialog-right {
    height: 400px;
  }
}

/* 自定义 el-dialog 样式 */
:deep(.el-dialog) {
  border-radius: 16px;
  max-width: 1200px;
}

:deep(.el-dialog__header) {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
  margin: 0;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__title) {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

:deep(.el-descriptions) {
  margin-bottom: 0;
}

:deep(.el-descriptions__cell) {
  padding: 12px 8px;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
  color: #6b7280;
}

:deep(.el-descriptions__content) {
  color: #1f2937;
  word-break: break-all;
}
</style> 